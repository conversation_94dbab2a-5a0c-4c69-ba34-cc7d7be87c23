import React, { useState, useEffect, useRef } from 'react';

/**
 * BottomSheet component for displaying content in a sliding panel from the bottom
 *
 * @param {boolean} isOpen - Whether the bottom sheet is open
 * @param {function} onClose - Function to call when the sheet should close
 * @param {string} title - Title to display at the top of the sheet
 * @param {React.ReactNode} children - Content to display inside the sheet
 * @param {number} height - Optional height of the sheet (default: 80vh)
 * @param {boolean} headerBorder - Optional border for header (default: true)
 */
const BottomSheet = ({ isOpen, onClose, title, children, height = '80vh', headerBorder = true }) => {
  const [isClosing, setIsClosing] = useState(false);
  const sheetRef = useRef(null);
  const startY = useRef(0);
  const currentY = useRef(0);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) {
      setIsClosing(false);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsClosing(true);

    // Apply slide down animation
    if (sheetRef.current) {
      sheetRef.current.style.transform = 'translateY(100%)';
    }

    setTimeout(() => {
      onClose();
    }, 300); // Match the transition duration
  };

  const handleTouchStart = (e) => {
    startY.current = e.touches[0].clientY;
    currentY.current = startY.current;

    // Remove transition during drag for smoother experience
    if (sheetRef.current) {
      sheetRef.current.style.transition = 'none';
    }
  };

  const handleTouchMove = (e) => {
    currentY.current = e.touches[0].clientY;
    const deltaY = currentY.current - startY.current;

    if (deltaY > 0) { // Only allow dragging down
      const sheet = sheetRef.current;
      if (sheet) {
        sheet.style.transform = `translateY(${deltaY}px)`;
      }
    }
  };

  const handleTouchEnd = () => {
    const deltaY = currentY.current - startY.current;
    const sheet = sheetRef.current;

    if (deltaY > 100) { // If dragged down more than 100px, close the sheet
      // Apply slide down animation
      sheet.style.transform = 'translateY(100%)';
      sheet.style.transition = 'transform 0.3s ease-out';
      handleClose();
    } else {
      // Otherwise snap back to original position
      sheet.style.transition = 'transform 0.3s ease-out';
      sheet.style.transform = 'translateY(0)';
    }
  };

  if (!isOpen && !isClosing) return null;

  return (
    <div
      className={`fixed inset-0 z-[999999] bg-black bg-opacity-50 transition-opacity duration-300 ${
        isClosing ? 'opacity-0' : 'opacity-100'
      }`}
      onClick={handleClose}
    >
      <div
        ref={sheetRef}
        className={`fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-lg transition-transform duration-300 ease-out ${
          isClosing ? 'transform translate-y-full' : 'transform translate-y-0'
        }`}
        style={{ maxHeight: height, height: height }}
        onClick={(e) => e.stopPropagation()}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Handle for dragging */}
        <div className="w-full flex justify-center py-2">
          <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* Header */}
        <div className={`px-3 py-0 flex justify-between items-center mb-1 ${headerBorder ? 'border-b border-gray-100' : ''}`}>
          <p className="text-[16px] font-semibold m-0">{title}</p>
          <button
            onClick={handleClose}
            className="w-8 h-8 rounded-lg flex items-center justify-center hover:bg-gray-100"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto" style={{ height: `calc(${height} - 60px)` }}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default BottomSheet;